//
// Created by <PERSON><PERSON><PERSON> on 25-7-25.
//

#ifndef MYDEFINITION_H
#define MYDEFINITION_H

#include <cmath>
#include <UHEMesh/HEMesh.h>
#include <UHEMesh/TVertex.h>
#include <UHEMesh/TEdge.h>
#include <UHEMesh/TPolygon.h>
#include <UHEMesh/THalfEdge.h>

namespace MyDefinition {

    class MyV;
    class MyE;
    class MyH;
    class MyP;

    using MyTraits = Ubpa::HEMeshTraits<MyV, MyE, MyP, MyH>;

    class MyV : public Ubpa::TVertex<MyTraits> {
    public:
        MyV() {}
        MyV(std::array<double, 3> position) : position(position) {}
        MyV(double x, double y, double z) {
            position[0] = x;
            position[1] = y;
            position[2] = z;
        }
        std::array<double, 3> getPosition() const {
            return position;
        }
        void setPosition(V*& pos) {
            this->position = pos->getPosition();
        }

    private:
        std::array<double, 3> position;
    };

    class MyE : public Ubpa::TEdge<MyTraits> {
    public:
        double Length() const { return length; }

        void setLength(double length) {
            this->length = length;
        }

        std::array<MyV *, 2> vertices1() const {
            return vertices;
        }

        void setVertices(const std::array<MyV *, 2> &vertices) {
            this->vertices = vertices;
            double px = vertices[0]->getPosition()[0];
            double py = vertices[0]->getPosition()[1];
            double pz = vertices[0]->getPosition()[2];
            px -= vertices[1]->getPosition()[0];
            py -= vertices[1]->getPosition()[1];
            pz -= vertices[1]->getPosition()[2];
            setLength(std::sqrt(px * px + py * py + pz * pz));
        }
        MyE(): length(0) {}
    private:
        double length;
        std::array<MyV*, 2> vertices;
    };
    class MyH : public Ubpa::THalfEdge<MyTraits> {};
    class MyP : public Ubpa::TPolygon<MyTraits> {
    public:
        std::array<MyV *, 3> Points() const {
            return points;
        }

        void setPoints(const std::array<MyV *, 3> &points) {
            this->points = points;
        }

        explicit MyP(const std::array<MyV *, 3> &points) {
            setPoints(points);
        }
    private:
        std::array<MyV*, 3> points {nullptr, nullptr, nullptr};
    };
}


#endif //MYDEFINITION_H

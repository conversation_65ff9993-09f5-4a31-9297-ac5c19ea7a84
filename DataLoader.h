#ifndef DATALOADER_H
#define DATALOADER_H

#include <fstream>
#include <sstream>
#include <iostream>
#include <vector>
#include <filesystem>
#include <array>
#include <string>
#include <UHEMesh/HEMesh.h>
#include "MyDefinition.h"

namespace fs = std::filesystem;

/**
 * 自定义封装数据类型
 * @tparam Traits 用户定义的字段
 */
template<typename Traits=MyDefinition::MyTraits>
class Answer {
public:
    using V = Ubpa::HEMeshTraits_V<Traits>;
    using E = Ubpa::HEMeshTraits_E<Traits>;
    using P = Ubpa::HEMeshTraits_P<Traits>;
    using H = Ubpa::HEMeshTraits_H<Traits>;

    std::vector<std::vector<V*>> vertices;
    std::vector<std::vector<P*>> polygons;
};

/**
 * 数据加载器
 * @tparam Traits 用户自定义类型
 */
template<typename Traits=MyDefinition::MyTraits>
class DataLoader {
public:
    using V = Ubpa::HEMeshTraits_V<Traits>;
    using E = Ubpa::HEMeshTraits_E<Traits>;
    using P = Ubpa::HEMeshTraits_P<Traits>;
    using H = Ubpa::HEMeshTraits_H<Traits>;
    using HEMesh = Ubpa::HEMesh<Traits>;

    /**
     * 解析面索引字符串，处理 vertex/texture/normal 格式
     * @param indexStr 索引字符串，如 "1", "1/2", "1/2/3", "1//3"
     * @param vertexCount 当前顶点总数，用于处理负索引
     * @return 顶点索引（转换为0基索引）
     */
    static int parseFaceIndex(const std::string& indexStr, int vertexCount) {
        size_t slashPos = indexStr.find('/');
        std::string vertexIndexStr = (slashPos != std::string::npos) ? 
                                     indexStr.substr(0, slashPos) : indexStr;
        
        try {
            int index = std::stoi(vertexIndexStr);
            // 处理负索引（相对索引）
            if (index < 0) {
                return vertexCount + index; // 负索引转换为正索引
            } else {
                return index - 1; // OBJ索引从1开始，转换为0基索引
            }
        } catch (const std::exception& e) {
            std::cerr << "Error parsing vertex index: " << vertexIndexStr << std::endl;
            return -1;
        }
    }

    /**
     * 从单个OBJ文件加载数据
     * @param objFilePath OBJ文件路径
     * @param vertices 顶点容器
     * @param polygons 面容器
     */
    static void LoadDataFromOneObjFile(const fs::path& objFilePath,
                                       std::vector<V*>& vertices,
                                       std::vector<P*>& polygons) {
        std::ifstream file(objFilePath);
        if (!file.is_open()) {
            std::cerr << "Error: Cannot open file " << objFilePath << std::endl;
            return;
        }
        
        std::string line;
        std::vector<std::array<double, 3>> vertexNormals; // 存储法线
        std::vector<std::array<double, 2>> textureCoords; // 存储纹理坐标
        
        while (std::getline(file, line)) {
            // 移除行首尾空白字符
            line.erase(0, line.find_first_not_of(" \t\r\n"));
            line.erase(line.find_last_not_of(" \t\r\n") + 1);
            
            // 跳过空行和注释行
            if (line.empty() || line[0] == '#') {
                continue;
            }
            
            std::istringstream iss(line);
            std::string prefix;
            iss >> prefix;
            
            if (prefix == "v") {
                // 处理顶点坐标
                double x, y, z;
                if (iss >> x >> y >> z) {
                    vertices.push_back(new V(x, y, z));
                } else {
                    std::cerr << "Warning: Invalid vertex line: " << line << std::endl;
                }
                
            } else if (prefix == "vn") {
                // 处理顶点法线
                double nx, ny, nz;
                if (iss >> nx >> ny >> nz) {
                    vertexNormals.push_back({nx, ny, nz});
                } else {
                    std::cerr << "Warning: Invalid normal line: " << line << std::endl;
                }
                
            } else if (prefix == "vt") {
                // 处理纹理坐标
                double u, v;
                if (iss >> u >> v) {
                    textureCoords.push_back({u, v});
                } else {
                    std::cerr << "Warning: Invalid texture coordinate line: " << line << std::endl;
                }
                
            } else if (prefix == "f") {
                // 处理面
                std::vector<int> faceIndices;
                std::string indexStr;
                
                while (iss >> indexStr) {
                    int vertexIndex = parseFaceIndex(indexStr, vertices.size());
                    if (vertexIndex >= 0 && vertexIndex < vertices.size()) {
                        faceIndices.push_back(vertexIndex);
                    } else {
                        std::cerr << "Warning: Invalid vertex index " << indexStr 
                                  << " in line: " << line << std::endl;
                    }
                }
                
                // 创建三角形面（三角化多边形）
                if (faceIndices.size() >= 3) {
                    // 第一个三角形
                    std::array<V*, 3> triangle;
                    triangle[0] = vertices[faceIndices[0]];
                    triangle[1] = vertices[faceIndices[1]];
                    triangle[2] = vertices[faceIndices[2]];
                    polygons.push_back(new P(triangle));
                    
                    // 如果是四边形或更多边形，进行扇形三角化
                    for (size_t i = 3; i < faceIndices.size(); ++i) {
                        triangle[1] = vertices[faceIndices[i-1]];
                        triangle[2] = vertices[faceIndices[i]];
                        polygons.push_back(new P(triangle));
                    }
                } else if (!faceIndices.empty()) {
                    std::cerr << "Warning: Face with less than 3 vertices: " << line << std::endl;
                }
                
            } else if (prefix == "g" || prefix == "o") {
                // 处理组名或对象名（目前只记录，不处理）
                std::string name;
                if (iss >> name) {
                    std::cout << "Found " << prefix << ": " << name << std::endl;
                }
                
            } else if (prefix == "s") {
                // 处理平滑组（目前只记录，不处理）
                std::string smoothGroup;
                if (iss >> smoothGroup) {
                    std::cout << "Smooth group: " << smoothGroup << std::endl;
                }
                
            } else if (prefix == "mtllib") {
                // 处理材质库文件（目前只记录，不处理）
                std::string mtlFile;
                if (iss >> mtlFile) {
                    std::cout << "Material library: " << mtlFile << std::endl;
                }
                
            } else if (prefix == "usemtl") {
                // 处理使用材质（目前只记录，不处理）
                std::string material;
                if (iss >> material) {
                    std::cout << "Using material: " << material << std::endl;
                }
                
            } else {
                // 未知行类型，记录但不抛出异常
                std::cout << "Info: Skipping unknown line type '" << prefix 
                          << "' in line: " << line << std::endl;
            }
        }
        
        file.close();
        
        // 输出统计信息
        std::cout << "Loaded: " << vertices.size() << " vertices, " 
                  << polygons.size() << " faces";
        if (!vertexNormals.empty()) {
            std::cout << ", " << vertexNormals.size() << " normals";
        }
        if (!textureCoords.empty()) {
            std::cout << ", " << textureCoords.size() << " texture coordinates";
        }
        std::cout << std::endl;
    }

    /**
     * 读取指定文件夹下所有子文件夹的obj文件，并记录点集以及多边形
     * @param DirPath 父文件夹路径
     * @return 自定义封装类型 Answer
     */
    static Answer<Traits> loadDataFromObjFiles(const std::string& DirPath) {
        Answer<Traits> answer;
        
        try {
            for (const auto& childDir : fs::directory_iterator(DirPath)) {
                if (childDir.is_directory()) {
                    std::cout << "Processing directory: " << childDir.path().filename() << std::endl;
                    
                    for (const auto& objFile : fs::directory_iterator(childDir.path())) {
                        if (objFile.is_regular_file() && 
                            objFile.path().extension() == ".obj") {
                            
                            std::cout << "Loading " << objFile.path().filename() << std::endl;
                            
                            std::vector<V*> vertices;
                            std::vector<P*> polygons;
                            LoadDataFromOneObjFile(objFile.path(), vertices, polygons);
                            
                            answer.vertices.push_back(vertices);
                            answer.polygons.push_back(polygons);
                        }
                    }
                }
            }
        } catch (const fs::filesystem_error& error) {
            std::cerr << "Filesystem error: " << error.what() << std::endl;
        } catch (const std::exception& error) {
            std::cerr << "Error: " << error.what() << std::endl;
        }
        
        // 输出总体统计信息
        std::cout << "\nSummary:" << std::endl;
        std::cout << "Successfully loaded " << answer.vertices.size() << " files" << std::endl;
        for (size_t i = 0; i < answer.vertices.size(); ++i) {
            std::cout << "  File " << i << ": " 
                      << answer.vertices[i].size() << " vertices, "
                      << answer.polygons[i].size() << " polygons" << std::endl;
        }
        
        return answer;
    }
};

#endif //DATALOADER_H
Explanation of the methods and models

The meaning of the suffix after the model name:


Input: The original model obtained by scanning, CAD or multi views.

mps: Maximal Poisson-disk Sampling
Reference: Gap processing for adaptive maximal Poisson-disk sampling

rar: Real-time Adaptive Remeshing
Reference: Adaptive remeshing for real-time mesh deformation

mai: Minimal Angle Improvement
Reference: Error bounded and feature preserving surface remeshing with minimal angle improvement

cvt: Centroidal Voronoi tessellation
Reference: Isotropic remeshing with fast and exact computation of restricted Voronoi diagram

nob: Non-OBtuse remeshing
Reference: Non-obtuse Remeshing with Centroidal Voronoi Tessellation

spp: Simple Push-Pull
Reference: A simple push-pull algorithm for blue-noise sampling

fpo: Farthest Point Optimization
Reference: Blue noise remeshing with farthest point optimization

ifm: Instant Field-aligned Meshes
Reference: Instant field-aligned meshes

_35-86: reach 35-86 degree after our method

_30-90: reach 30-90 degree after our method
Reference: Isotropic Surface Remeshing without Large and Small Angles

Note that different methods have different initial normalization or initial processing, so the scale of the model is not the same
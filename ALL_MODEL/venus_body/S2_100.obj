# vorpaline algo:delaunay=NN
# vorpaline algo:hole_filling=loop_split
# vorpaline algo:nn_search=BNN
# vorpaline algo:parallel=true
# vorpaline algo:predicates=fast
# vorpaline algo:reconstruct=Co3Ne
# vorpaline base_modules=luagrob;mesh;voxel
# vorpaline batch=false
# vorpaline biblio=false
# vorpaline biblio:command_line=false
# vorpaline co3ne=false
# vorpaline co3ne:Psmooth_iter=0
# vorpaline co3ne:T12=true
# vorpaline co3ne:max_N_angle=60
# vorpaline co3ne:max_hole_area=5%
# vorpaline co3ne:max_hole_edges=500
# vorpaline co3ne:min_comp_area=0.01%
# vorpaline co3ne:min_comp_facets=10
# vorpaline co3ne:nb_neighbors=30
# vorpaline co3ne:radius=5%
# vorpaline co3ne:repair=true
# vorpaline co3ne:strict=false
# vorpaline co3ne:use_normals=true
# vorpaline debug=false
# vorpaline gel=gel_lua
# vorpaline gfx:GLSL=true
# vorpaline gfx:GLSL_tesselation=true
# vorpaline gfx:GLSL_version=0
# vorpaline gfx:GLUP_profile=auto
# vorpaline gfx:GL_debug=false
# vorpaline gfx:GL_profile=compatibility
# vorpaline gfx:GL_version=0
# vorpaline gfx:full_screen=false
# vorpaline gfx:geometry=800x800
# vorpaline hex=false
# vorpaline hex:PGP_FF_fixed_topo=1
# vorpaline hex:PGP_FF_free_topo=1
# vorpaline hex:PGP_direct_solver=false
# vorpaline hex:PGP_max_corr_prop=0.34999999999999998
# vorpaline hex:algo=PGP3d
# vorpaline hex:border_max_distance=20%
# vorpaline hex:border_refine=false
# vorpaline hex:constrained=true
# vorpaline hex:frames_file=
# vorpaline hex:keep_border=false
# vorpaline hex:points_file=
# vorpaline hex:prefer_seeds=true
# vorpaline hex:prisms=false
# vorpaline hex:pyramids=false
# vorpaline hex:save_frames=false
# vorpaline hex:save_points=false
# vorpaline hex:save_surface=false
# vorpaline hex:save_tets=false
# vorpaline hex:tets_file=
# vorpaline interactive=false
# vorpaline log:features=*
# vorpaline log:features_exclude=
# vorpaline log:file_name=
# vorpaline log:pretty=true
# vorpaline log:quiet=false
# vorpaline main=lib/graphite.lua
# vorpaline nl:CUDA=false
# vorpaline nl:MKL=false
# vorpaline opt:Newton_m=7
# vorpaline opt:nb_Lloyd_iter=5
# vorpaline opt:nb_LpCVT_iter=30
# vorpaline opt:nb_Newton_iter=30
# vorpaline poisson:octree_depth=8
# vorpaline post=true
# vorpaline post:compute_normals=false
# vorpaline post:isect=false
# vorpaline post:max_deg3_dist=0.10000000000000001%
# vorpaline post:max_hole_area=0%
# vorpaline post:max_hole_edges=2000
# vorpaline post:min_comp_area=0%
# vorpaline post:repair=false
# vorpaline pre=true
# vorpaline pre:Nsmooth_iter=1
# vorpaline pre:brutal_kill_borders=0
# vorpaline pre:epsilon=0%
# vorpaline pre:margin=0%
# vorpaline pre:max_hole_area=0%
# vorpaline pre:max_hole_edges=2000
# vorpaline pre:min_comp_area=0%
# vorpaline pre:repair=false
# vorpaline pre:vcluster_bins=0
# vorpaline profile=scan
# vorpaline remesh=true
# vorpaline remesh:RVC_centroids=true
# vorpaline remesh:anisotropy=0
# vorpaline remesh:by_parts=false
# vorpaline remesh:gradation=0
# vorpaline remesh:lfs_samples=10000
# vorpaline remesh:max_dist=0.20000000000000001
# vorpaline remesh:multi_nerve=true
# vorpaline remesh:nb_pts=30000
# vorpaline remesh:refine=false
# vorpaline shell=false
# vorpaline skin=
# vorpaline stat:sampling_step=0.5%
# vorpaline sys:FPE=false
# vorpaline sys:assert=abort
# vorpaline sys:cancel=false
# vorpaline sys:compression_level=3
# vorpaline sys:lowmem=false
# vorpaline sys:max_threads=8
# vorpaline sys:multithread=true
# vorpaline sys:stats=false
# vorpaline sys:use_doubles=false
# vorpaline tet=false
# vorpaline tet:preprocess=true
# vorpaline tet:quality=2
# vorpaline tet:refine=true
v 0.574716 0.846222 0.837931 
v 0.716781 0.706802 0.885775 
v 0.746301 0.828863 0.76602 
v 0.544049 0.718017 0.936253 
v 0.393268 0.803668 0.866858 
v 0.450144 0.932917 0.722509 
v 0.62016 0.931744 0.698386 
v 0.810303 0.546785 0.875469 
v 0.867356 0.677826 0.770325 
v 0.629443 0.565514 0.967444 
v 0.746954 0.914048 0.586391 
v 0.861675 0.796309 0.64366 
v 0.424679 0.612986 0.970247 
v 0.267798 0.645985 0.904665 
v 0.237245 0.785526 0.798638 
v 0.290962 0.900836 0.68689 
v 0.39019 0.974527 0.537889 
v 0.567536 0.982495 0.547374 
v 0.939527 0.521642 0.713437 
v 0.860373 0.385016 0.810476 
v 0.706861 0.410661 0.933812 
v 0.952067 0.664035 0.589865 
v 0.497951 0.442704 0.98597 
v 0.708838 0.934749 0.421421 
v 0.867639 0.8218 0.475387 
v 0.321022 0.459409 0.953625 
v 0.167961 0.498335 0.85868 
v 0.124326 0.656006 0.772277 
v 0.136397 0.794077 0.64502 
v 0.220013 0.900369 0.517523 
v 0.332249 0.938099 0.360643 
v 0.516163 0.973459 0.377919 
v 0.951847 0.361273 0.62622 
v 0.988252 0.508427 0.516909 
v 0.849067 0.235388 0.717428 
v 0.726519 0.248461 0.852216 
v 0.57075 0.299118 0.94088 
v 0.946775 0.675447 0.406185 
v 0.386696 0.278608 0.920209 
v 0.647971 0.902734 0.265472 
v 0.819994 0.81898 0.311532 
v 0.216513 0.33718 0.864523 
v 0.065216 0.496078 0.724573 
v 0.0977975 0.326462 0.717744 
v 0.0413039 0.634709 0.604571 
v 0.0864796 0.758944 0.473467 
v 0.187041 0.845691 0.351925 
v 0.264924 0.80546 0.199166 
v 0.438654 0.900113 0.224747 
v 0.959082 0.348756 0.42367 
v 0.896771 0.216246 0.52537 
v 0.958617 0.505898 0.331268 
v 0.784155 0.118261 0.617297 
v 0.675118 0.113142 0.743859 
v 0.545236 0.161778 0.850867 
v 0.880657 0.659769 0.237413 
v 0.385245 0.131205 0.800418 
v 0.240526 0.19673 0.783742 
v 0.579629 0.823265 0.141651 
v 0.742758 0.758888 0.162776 
v 0.0189204 0.446482 0.567723 
v 0.0675662 0.272671 0.529713 
v 0.163559 0.172956 0.639982 
v 0.0238563 0.586453 0.427788 
v 0.106711 0.703454 0.291496 
v 0.308118 0.667671 0.0825601 
v 0.17552 0.615248 0.152981 
v 0.431674 0.768067 0.0964407 
v 0.883601 0.340441 0.2418 
v 0.858895 0.206897 0.341883 
v 0.766247 0.0923268 0.451405 
v 0.868246 0.49239 0.177712 
v 0.636051 0.0348716 0.570185 
v 0.501477 0.0508957 0.694619 
v 0.759707 0.588058 0.0950172 
v 0.316896 0.0679575 0.634547 
v 0.609096 0.673773 0.0556394 
v 0.0323877 0.397276 0.398659 
v 0.188462 0.123537 0.480258 
v 0.124484 0.222987 0.35444 
v 0.0726402 0.522734 0.262244 
v 0.314712 0.497104 0.0466245 
v 0.465163 0.594534 0.0209335 
v 0.19128 0.425928 0.127754 
v 0.740036 0.231365 0.168806 
v 0.746131 0.382565 0.0941895 
v 0.716996 0.114748 0.288727 
v 0.595467 0.0353164 0.383774 
v 0.470735 0.0121181 0.5162 
v 0.625723 0.482427 0.0270991 
v 0.329298 0.0439353 0.446956 
v 0.106226 0.351152 0.249261 
v 0.270914 0.119165 0.295173 
v 0.230732 0.244024 0.18182 
v 0.353869 0.312986 0.0725188 
v 0.475157 0.413139 0.0185307 
v 0.570752 0.274448 0.0734244 
v 0.575228 0.127004 0.19266 
v 0.442546 0.0546048 0.304886 
v 0.407073 0.167424 0.153751 
f 1 3 2 
f 2 4 1 
f 4 5 1 
f 5 6 1 
f 6 7 1 
f 1 7 3 
f 3 9 2 
f 2 9 8 
f 8 10 2 
f 2 10 4 
f 7 11 3 
f 11 12 3 
f 3 12 9 
f 10 13 4 
f 4 13 5 
f 13 14 5 
f 14 15 5 
f 15 16 5 
f 5 16 6 
f 16 17 6 
f 17 18 6 
f 7 18 11 
f 6 18 7 
f 9 19 8 
f 19 20 8 
f 20 21 8 
f 8 21 10 
f 12 22 9 
f 9 22 19 
f 21 23 10 
f 10 23 13 
f 18 24 11 
f 11 25 12 
f 12 25 22 
f 24 25 11 
f 23 26 13 
f 13 26 14 
f 26 27 14 
f 27 28 14 
f 14 28 15 
f 28 29 15 
f 15 29 16 
f 29 30 16 
f 16 30 17 
f 30 31 17 
f 31 32 17 
f 17 32 18 
f 18 32 24 
f 19 33 20 
f 19 34 33 
f 22 34 19 
f 33 35 20 
f 35 36 20 
f 20 36 21 
f 36 37 21 
f 21 37 23 
f 25 38 22 
f 22 38 34 
f 37 39 23 
f 23 39 26 
f 32 40 24 
f 40 41 24 
f 25 41 38 
f 24 41 25 
f 39 42 26 
f 26 42 27 
f 27 43 28 
f 42 44 27 
f 27 44 43 
f 43 45 28 
f 28 45 29 
f 45 46 29 
f 29 46 30 
f 46 47 30 
f 30 47 31 
f 47 48 31 
f 31 49 32 
f 48 49 31 
f 32 49 40 
f 34 50 33 
f 50 51 33 
f 33 51 35 
f 38 52 34 
f 34 52 50 
f 51 53 35 
f 53 54 35 
f 35 54 36 
f 54 55 36 
f 37 55 39 
f 36 55 37 
f 41 56 38 
f 38 56 52 
f 55 57 39 
f 57 58 39 
f 42 58 44 
f 39 58 42 
f 49 59 40 
f 59 60 40 
f 40 60 41 
f 41 60 56 
f 44 61 43 
f 43 61 45 
f 44 62 61 
f 44 63 62 
f 58 63 44 
f 61 64 45 
f 45 64 46 
f 64 65 46 
f 47 65 48 
f 46 65 47 
f 65 67 48 
f 48 67 66 
f 66 68 48 
f 48 68 49 
f 49 68 59 
f 52 69 50 
f 69 70 50 
f 50 70 51 
f 70 71 51 
f 51 71 53 
f 56 72 52 
f 52 72 69 
f 71 73 53 
f 53 73 54 
f 54 74 55 
f 55 74 57 
f 73 74 54 
f 56 75 72 
f 60 75 56 
f 74 76 57 
f 58 76 63 
f 57 76 58 
f 68 77 59 
f 59 77 60 
f 60 77 75 
f 62 78 61 
f 61 78 64 
f 63 79 62 
f 76 79 63 
f 79 80 62 
f 62 80 78 
f 78 81 64 
f 65 81 67 
f 64 81 65 
f 67 82 66 
f 82 83 66 
f 68 83 77 
f 66 83 68 
f 81 84 67 
f 67 84 82 
f 69 85 70 
f 72 86 69 
f 69 86 85 
f 75 86 72 
f 85 87 70 
f 70 87 71 
f 87 88 71 
f 71 88 73 
f 88 89 73 
f 74 89 76 
f 73 89 74 
f 77 90 75 
f 83 90 77 
f 75 90 86 
f 89 91 76 
f 76 91 79 
f 80 92 78 
f 81 92 84 
f 78 92 81 
f 91 93 79 
f 79 93 80 
f 93 94 80 
f 92 94 84 
f 80 94 92 
f 84 95 82 
f 94 95 84 
f 95 96 82 
f 83 96 90 
f 82 96 83 
f 86 97 85 
f 90 97 86 
f 96 97 90 
f 95 97 96 
f 97 98 85 
f 87 98 88 
f 85 98 87 
f 98 99 88 
f 91 99 93 
f 89 99 91 
f 88 99 89 
f 99 100 93 
f 98 100 99 
f 94 100 95 
f 97 100 98 
f 95 100 97 
f 93 100 94 
